import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AttendanceRecord } from '@/services/attendanceService';
import { useColorScheme } from 'react-native';
import Colors from '@/constants/Colors';
import { Clock, MapPin, Calendar } from 'lucide-react-native';

interface AttendanceCardProps {
  record: AttendanceRecord;
}

export default function AttendanceCard({ record }: AttendanceCardProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };
  
  // Format time
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  return (
    <View style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={styles.header}>
        <View style={styles.typeContainer}>
          <Text style={[styles.type, { color: colors.primary }]}>
            {record.type === 'work' ? 'Work' : 'Event'}
          </Text>
          <View 
            style={[
              styles.statusBadge, 
              { 
                backgroundColor: record.status === 'checked-in' 
                  ? colors.success + '20'
                  : colors.info + '20',
                borderColor: record.status === 'checked-in'
                  ? colors.success
                  : colors.info
              }
            ]}
          >
            <Text style={[
              styles.statusText,
              { 
                color: record.status === 'checked-in' 
                  ? colors.success
                  : colors.info
              }
            ]}>
              {record.status === 'checked-in' ? 'Checked In' : 'Checked Out'}
            </Text>
          </View>
        </View>
        {record.eventName && (
          <Text style={[styles.eventName, { color: colors.text }]}>
            {record.eventName}
          </Text>
        )}
      </View>
      
      <View style={styles.details}>
        <View style={styles.detailRow}>
          <Calendar size={16} color={colors.primary} />
          <Text style={[styles.detailText, { color: colors.text }]}>
            {formatDate(record.checkInTime)}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Clock size={16} color={colors.primary} />
          <Text style={[styles.detailText, { color: colors.text }]}>
            {formatTime(record.checkInTime)}
            {record.checkOutTime && ` - ${formatTime(record.checkOutTime)}`}
          </Text>
        </View>
        
        {record.location && (
          <View style={styles.detailRow}>
            <MapPin size={16} color={colors.primary} />
            <Text style={[styles.detailText, { color: colors.text }]}>
              {record.location}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    marginBottom: 12,
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  type: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  eventName: {
    fontSize: 18,
    fontWeight: '700',
    marginTop: 4,
  },
  details: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
  },
});