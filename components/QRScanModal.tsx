import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useColorScheme } from 'react-native';
import Colors from '@/constants/Colors';
import { QrCode, X } from 'lucide-react-native';
import QRScanner from './QRScanner';
import CheckInButton from './CheckInButton';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface QRScanModalProps {
  isVisible: boolean;
  onClose: () => void;
  onScan: (data: string) => void;
  isLoading: boolean;
  type: 'work' | 'event';
}

const { width } = Dimensions.get('window');

export default function QRScanModal({
  isVisible,
  onClose,
  onScan,
  isLoading,
  type,
}: QRScanModalProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const insets = useSafeAreaInsets();

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View
        style={[
          styles.container,
          {
            backgroundColor: colors.background,
            paddingTop: insets.top,
          },
        ]}
      >
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
          <Text
            style={[
              styles.title,
              { color: colors.text, fontFamily: 'Inter-Bold' },
            ]}
          >
            {type === 'work' ? 'Work Check-In' : 'Event Check-In'}
          </Text>
        </View>

        <View style={styles.content}>
          <View
            style={[styles.scannerContainer, { backgroundColor: colors.card }]}
          >
            <QRScanner onScan={onScan} isVisible={isVisible} />
          </View>

          <View
            style={[styles.instructionsCard, { backgroundColor: colors.card }]}
          >
            <QrCode size={32} color={colors.primary} style={styles.icon} />
            <Text
              style={[
                styles.instructionsTitle,
                { color: colors.text, fontFamily: 'Inter-SemiBold' },
              ]}
            >
              Scan QR Code to Check In
            </Text>
            <Text
              style={[
                styles.instructionsText,
                { color: colors.placeholder, fontFamily: 'Inter-Regular' },
              ]}
            >
              Point your camera at the QR code at your workplace to record your
              attendance
            </Text>
            <CheckInButton
              title={`Confirm ${type === 'work' ? 'Work' : 'Event'} Check-In`}
              onPress={() => {}}
              isLoading={isLoading}
              style={styles.confirmButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  closeButton: {
    padding: 8,
    marginRight: 8,
  },
  title: {
    fontSize: 20,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  scannerContainer: {
    height: width * 0.8,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
  },
  instructionsCard: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  icon: {
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: 18,
    marginBottom: 8,
    textAlign: 'center',
  },
  instructionsText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  confirmButton: {
    width: '100%',
  },
});
