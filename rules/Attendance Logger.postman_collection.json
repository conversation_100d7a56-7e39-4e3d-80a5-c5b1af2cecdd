{"info": {"_postman_id": "52c990d3-f5fd-407c-ab29-0a1fe3b71b94", "name": "Attendance Logger", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26455483", "_collection_link": "https://gold-shuttle-273267.postman.co/workspace/NetOne~4eabe34f-b52f-4244-bb2b-96b5cc35ee6e/collection/25434746-52c990d3-f5fd-407c-ab29-0a1fe3b71b94?action=share&source=collection_link&creator=26455483"}, "item": [{"name": "Register Employee", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"ecNumber\": \"157638\",\n    \"password\": \"\",\n    \"deviceId\": \"5668986\",\n    \"fullName\": \"<PERSON> <PERSON><PERSON><PERSON>\",\n    \"isAdmin\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/auth/register", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "auth", "register"]}, "description": "### Register User Endpoint\n\nThis endpoint allows for the registration of a new user in the system. It requires the submission of specific user details in the request body. Upon successful registration, the response will include a JSON Web Token (JWT) that can be used for subsequent user requests related to check-in or check-out. This JWT does not expire until the user changes devices.\n\n#### Request Parameters\n\n- **ecNumber** (string, required): The employee code number of the user.\n    \n- **password** (string, required): The password for the user account.\n    \n- **deviceId** (string, required): The unique identifier for the device being registered.\n    \n- **fullName** (string, required): The full name of the user.\n    \n- **isAdmin** (boolean, required): A flag indicating if the user is an admin.\n    \n    - If `true`, the user has administrative privileges, allowing them to view all details and add attendance devices to the system.\n        \n    - If `false`, the user is considered a normal user, with permissions limited to registering devices, checking in, and checking out.\n        \n\n#### Response\n\n- **Status**: 200 OK\n    \n- **Content-Type**: application/json\n    \n- **Body**:\n    \n    - `success` (boolean): Indicates whether the registration was successful.\n        \n    - `message` (string): A message related to the registration process (may be empty).\n        \n    - `body` (string): Additional information related to the response (may be empty).\n        \n\nAll fields in the request are mandatory for successful registration.\n\nThis endpoint is used to register a new user in the system. It accepts user details in the request body and returns a success message upon successful registration.\n\n### Request\n\n- **Method**: POST\n    \n- **URL**: `http://************:8170/api/auth/register`\n    \n\n#### Request Body\n\nThe request body must be in JSON format and include the following parameters:\n\n- `ecNumber` (string): The employee number or identification number of the user.\n    \n- `password` (string): The password for the user's account. This field can be left empty, but it is recommended to provide a secure password.\n    \n- `deviceId` (string): A unique identifier for the device being used to register.\n    \n- `fullName` (string): The full name of the user being registered.\n    \n- `isAdmin` (boolean): A flag indicating whether the user has administrative privileges (true for admin, false for regular user).\n    \n\n**Example Request Body**:\n\n``` json\n{\n  \"ecNumber\": \"157638\",\n  \"password\": \"\",\n  \"deviceId\": \"5668986\",\n  \"fullName\": \"Blessed Tawanda Mahuni\",\n  \"isAdmin\": true\n}\n\n ```\n\n### Response\n\nUpon successful registration, the API will respond with a JSON object containing the following fields:\n\n- `success` (boolean): Indicates whether the registration was successful.\n    \n- `message` (string): A message providing additional information about the registration process (may be empty).\n    \n- `body` (string): Registration token used to make check-in and check-out requests, does not expire.\n    \n\n**Example Response**:\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": \"\"\n}\n\n ```\n\n### Status Codes\n\n- **200 OK**: The registration was successful.\n    \n- Other status codes may indicate errors or issues with the registration process.\n    \n\nEnsure that all required fields are provided in the request to avoid errors during registration."}, "response": []}, {"name": "Get Employees", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:8170/api/employees", "protocol": "http", "host": ["localhost"], "port": "8170", "path": ["api", "employees"]}, "description": "# Get Employees\n\nThis endpoint retrieves a list of employees from the system. It is a simple HTTP GET request that does not require any request parameters.\n\n## Request\n\n- **Method**: GET\n    \n- **URL**: `http://localhost:8170/api/employees`\n    \n\n## Response\n\nOn a successful request, the server responds with a status code of `200` and a JSON object containing the following structure:\n\n- **success** (boolean): Indicates whether the request was successful.\n    \n- **message** (string): A message providing additional information (if any).\n    \n- **body** (array): An array of employee objects, where each employee object contains:\n    \n    - **id** (string): The unique identifier for the employee.\n        \n    - **ecNumber** (string): The employee's identification number.\n        \n    - **fullName** (string): The full name of the employee.\n        \n    - **devices** (array): A list of devices associated with the employee.\n        \n    - **role** (string): The role of the employee within the organization.\n        \n    - **password** (null): The password field, which is not returned for security reasons.\n        \n    - **authorities** (array): An array of authority objects, where each authority object contains:\n        \n        - **authority** (string): The authority granted to the employee.\n            \n    - **username** (string): The username of the employee.\n        \n    - **enabled** (boolean): Indicates if the employee account is enabled.\n        \n    - **credentialsNonExpired** (boolean): Indicates if the employee's credentials have not expired.\n        \n    - **accountNonExpired** (boolean): Indicates if the employee's account has not expired.\n        \n    - **accountNonLocked** (boolean): Indicates if the employee's account is not locked.\n        \n\n### Notes\n\n- Ensure that the API server is running and accessible at the specified URL.\n    \n- The response will include a list of employees, and the structure may vary based on the data available in the system."}, "response": [{"name": "Get Employees", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8170/api/employees", "protocol": "http", "host": ["localhost"], "port": "8170", "path": ["api", "employees"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 11 Jun 2025 09:12:28 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"List of all employees\",\n    \"body\": [\n        {\n            \"id\": \"68494870795629c13082983e\",\n            \"ecNumber\": \"157638\",\n            \"fullName\": \"<PERSON> <PERSON><PERSON><PERSON>\",\n            \"devices\": [\n                \"5668986\"\n            ],\n            \"role\": \"ROLE_ADMIN\",\n            \"password\": null,\n            \"authorities\": [\n                {\n                    \"authority\": \"ROLE_ADMIN\"\n                }\n            ],\n            \"username\": \"157638\",\n            \"enabled\": true,\n            \"credentialsNonExpired\": true,\n            \"accountNonExpired\": true,\n            \"accountNonLocked\": true\n        }\n    ]\n}"}]}, {"name": "Register Attendance Device", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"location\": \"Kopje Plaza HQ\",\n    \"floor\": \"10\",\n    \"deviceId\": \"My Device\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-devices/register", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-devices", "register"]}, "description": "## Register Attendance Device\n\nThis endpoint is used to register an attendance device within the system. It is important to note that this operation is intended for **admin use only**.\n\n### Request\n\n**Method:** POST  \n**URL:** `http://************:8170/api/attendance-devices/register`\n\n**Request Body:**  \nThe request body must be a JSON object containing the following parameters:\n\n- **location** (string): The location where the device is installed.\n    \n- **floor** (string): The floor number of the installation.\n    \n- **deviceId** (string): The unique identifier for the device.\n    \n\n**Example:**\n\n``` json\n{\n  \"location\": \"Kopje Plaza HQ\",\n  \"floor\": \"10\",\n  \"deviceId\": \"My Device\"\n}\n\n ```\n\n### Response\n\nUpon successful registration, the API will return a JSON response with the following structure:\n\n- **success** (boolean): Indicates whether the operation was successful.\n    \n- **message** (string): A message providing additional information (if any).\n    \n- **body** (object): Contains details about the registered device:\n    \n    - **id** (string): The unique identifier assigned to the registered device.\n        \n    - **deviceId** (string): The device ID that was registered.\n        \n    - **location** (string): The location of the registered device.\n        \n    - **floor** (string): The floor of the registered device.\n        \n\n**Example Response:**\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": {\n    \"id\": \"\",\n    \"deviceId\": \"\",\n    \"location\": \"\",\n    \"floor\": \"\"\n  }\n}\n\n ```\n\nThis endpoint allows you to register a new attendance device at a specified location. It accepts details about the device, including its location, floor, and unique device identifier.\n\n### Request\n\n- **Method:** POST\n    \n- **URL:** `http://************:8170/api/attendance-devices/register`\n    \n\n#### Request Body\n\nThe request body must be in JSON format and include the following parameters:\n\n- **location** (string): The name of the location where the device is installed.\n    \n- **floor** (string): The floor number of the location.\n    \n- **deviceId** (string): A unique identifier for the device.\n    \n\n**Example Request Body:**\n\n``` json\n{\n  \"location\": \"Kopje Plaza HQ\",\n  \"floor\": \"10\",\n  \"deviceId\": \"My Device\"\n}\n\n ```\n\n### Response\n\nUpon successful registration, the API will return a response with the following structure:\n\n- **Status:** 200 OK\n    \n- **Content-Type:** application/json\n    \n\n#### Response Body\n\nThe response body will contain:\n\n- **success** (boolean): Indicates whether the registration was successful.\n    \n- **message** (string): A message providing additional context (can be empty).\n    \n- **body** (object): Contains the details of the registered device:\n    \n    - **id** (string): The unique identifier assigned to the registered device.\n        \n    - **deviceId** (string): The device identifier provided in the request.\n        \n    - **location** (string): The location where the device is registered.\n        \n    - **floor** (string): The floor number where the device is located.\n        \n\n**Example Response Body:**\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": {\n    \"id\": \"\",\n    \"deviceId\": \"\",\n    \"location\": \"\",\n    \"floor\": \"\"\n  }\n}\n\n ```\n\nEnsure that all required fields are provided in the request to successfully register the attendance device."}, "response": [{"name": "Register Attendance Device", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"location\": \"Kopje Plaza HQ\",\n    \"floor\": \"10\",\n    \"deviceId\": \"My Device\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-devices/register", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-devices", "register"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 11 Jun 2025 12:56:34 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Attendance device registered successfully\",\n    \"body\": {\n        \"id\": \"68497d02bad96dad988d3779\",\n        \"deviceId\": \"My Device\",\n        \"location\": \"Kopje Plaza HQ\",\n        \"floor\": \"10\"\n    }\n}"}]}, {"name": "Get Registration Device", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://************:8170/api/attendance-devices", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-devices"]}, "description": "## API Endpoint: Get Attendance Devices\n\n### Purpose\n\nThis endpoint retrieves a list of attendance devices registered in the system. It provides essential information about each device, including its unique identifier, device ID, location, and floor.\n\n### Request Format\n\n- **Method:** GET\n    \n- **URL:** `http://************:8170/api/attendance-devices`\n    \n- **Request Body:** No request body is required for this GET request.\n    \n\n### Response Format\n\n- **Status Code:** 200 OK\n    \n- **Content-Type:** application/json\n    \n\n#### Response Structure\n\nThe response will be a JSON object with the following structure:\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": [\n    {\n      \"id\": \"\",\n      \"deviceId\": \"\",\n      \"location\": \"\",\n      \"floor\": \"\"\n    }\n  ]\n}\n\n ```\n\n- **success**: A boolean indicating whether the request was successful.\n    \n- **message**: A string that may contain additional information or error messages (if any).\n    \n- **body**: An array of objects, each representing an attendance device with the following fields:\n    \n    - **id**: The unique identifier of the device.\n        \n    - **deviceId**: The ID assigned to the device.\n        \n    - **location**: The physical location of the device.\n        \n    - **floor**: The floor on which the device is located.\n        \n\nThis endpoint is useful for applications that need to display or manage attendance devices within a specific environment."}, "response": []}, {"name": "Check In", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"attendanceCode\": \"QR_My Device_2025-06-11\",\n    \"deviceId\": \"5668986\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-log/check-in", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "check-in"]}, "description": "## Check-In Attendance Log\n\nThis endpoint allows users to record their check-in attendance using a specific attendance code and device ID.\n\n### Request\n\n**Method:** POST  \n**URL:** `http://************:8170/api/attendance-log/check-in`\n\n**Request Body:**  \nThe request requires a JSON payload with the following parameters:\n\n- `attendanceCode` (string): This should follow the format `QR__`, where is the unique identifier for the attendance device and is the date in `YYYY-MM-DD` format.\n    \n- `deviceId` (string): The unique identifier of the device being used for check-in.\n    \n\n### Response\n\nUpon a successful check-in, the API will return a response with the following structure:\n\n- **Status:** 200\n    \n- **Content-Type:** application/json\n    \n- **Body:**\n    \n    - `success` (boolean): Indicates whether the check-in was successful.\n        \n    - `message` (string): A message related to the check-in process, which may be empty on success.\n        \n    - `body` (object): Contains detailed information about the check-in, including:\n        \n        - `id` (string): The unique identifier for the attendance record.\n            \n        - `employee` (object): Information about the employee, including:\n            \n            - `id` (string): Unique identifier for the employee.\n                \n            - `ecNumber` (string): Employee code number.\n                \n            - `fullName` (string): Full name of the employee.\n                \n            - `devices` (array): List of devices associated with the employee.\n                \n            - `role` (string): The role of the employee.\n                \n            - `username` (string): Username of the employee.\n                \n            - `enabled` (boolean): Indicates if the account is enabled.\n                \n            - `authorities` (array): List of authorities granted to the employee.\n                \n        - `attendanceDevice` (object): Information about the attendance device used.\n            \n            - `id` (string): Unique identifier for the attendance device.\n                \n            - `deviceId` (string): The device ID.\n                \n            - `location` (string): Location of the device.\n                \n            - `floor` (string): Floor where the device is located.\n                \n        - `checkInDate` (string): The date of check-in.\n            \n        - `checkInTime` (string): The time of check-in.\n            \n        - `checkOutTime` (string|null): The time of check-out, if applicable.\n            \n        - `actualLocation` (string|null): The actual location of the employee during check-in, if applicable.\n            \n        - `attendanceCode` (string): The attendance code used for check-in.\n            \n\n### Error Response\n\nIf a user has already checked in for the day, the API will return the following response:\n\n- **Body:**\n    \n    - `success` (boolean): `false`\n        \n    - `message` (string): \"Check-in already recorded for today.\"\n        \n    - `body` (null): No additional information provided.\n        \n\nThis documentation helps users understand the expected input format for the attendance code and the potential responses from the API.\n\n**Example Request Body:**\n\n``` json\n{\n  \"attendanceCode\": \"QR_My Device_2025-06-11\",\n  \"deviceId\": \"5668986\"\n}\n\n ```\n\n**Example Response:**\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": {\n    \"id\": \"\",\n    \"employee\": {\n      \"id\": \"\",\n      \"ecNumber\": \"\",\n      \"fullName\": \"\",\n      \"devices\": [\"\"],\n      \"role\": \"\",\n      \"username\": \"\",\n      \"enabled\": true,\n      \"authorities\": [{\"authority\": \"\"}]\n    },\n    \"attendanceDevice\": {\n      \"id\": \"\",\n      \"deviceId\": \"\",\n      \"location\": \"\",\n      \"floor\": \"\"\n    },\n    \"checkInDate\": \"\",\n    \"checkInTime\": \"\",\n    \"checkOutTime\": null,\n    \"actualLocation\": null,\n    \"attendanceCode\": \"\"\n  }\n}\n\n ```\n\n#### Notes\n\n- Ensure that the `attendanceCode` and `deviceId` are correctly formatted to avoid errors during the check-in process.\n    \n- The response may vary based on the success of the check-in and the data associated with the employee and device."}, "response": [{"name": "Already Checked In Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"attendanceCode\": \"QR_My Device_2025-06-11\",\n    \"deviceId\": \"5668986\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-log/check-in", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "check-in"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 11 Jun 2025 12:57:11 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Check-in already recorded for today.\",\n    \"body\": null\n}"}, {"name": "Succesful Check In", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"attendanceCode\": \"QR_My Device_2025-06-11\",\n    \"deviceId\": \"5668986\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-log/check-in", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "check-in"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 11 Jun 2025 13:32:31 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Check-in successful\",\n    \"body\": {\n        \"id\": \"6849856fbad96dad988d377b\",\n        \"employee\": {\n            \"id\": \"68497ce2bad96dad988d3778\",\n            \"ecNumber\": \"157638\",\n            \"fullName\": \"Blessed <PERSON><PERSON>\",\n            \"devices\": [\n                \"5668986\"\n            ],\n            \"role\": \"ROLE_ADMIN\",\n            \"password\": null,\n            \"authorities\": [\n                {\n                    \"authority\": \"ROLE_ADMIN\"\n                }\n            ],\n            \"username\": \"157638\",\n            \"enabled\": true,\n            \"accountNonExpired\": true,\n            \"accountNonLocked\": true,\n            \"credentialsNonExpired\": true\n        },\n        \"attendanceDevice\": {\n            \"id\": \"68497d02bad96dad988d3779\",\n            \"deviceId\": \"My Device\",\n            \"location\": \"Kopje Plaza HQ\",\n            \"floor\": \"10\"\n        },\n        \"checkInDate\": \"2025-06-11\",\n        \"checkInTime\": \"15:32:31.*********\",\n        \"checkOutTime\": null,\n        \"actualLocation\": null,\n        \"attendanceCode\": \"QR_My Device_2025-06-11\"\n    }\n}"}]}, {"name": "Check Out", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"attendanceCode\": \"QR_My Device_2025-06-11\",\n    \"deviceId\": \"5668986\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-log/check-out", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "check-out"]}, "description": "## Check-Out Attendance Log\n\nThis endpoint allows users to check out from an attendance log using a specific attendance code and device ID.\n\n### Request\n\n**Method:** POST  \n**URL:** `http://************:8170/api/attendance-log/check-out`\n\n#### Request Body\n\nThe request body must be in JSON format and should include the following parameters:\n\n- **attendanceCode** (string): The code used for attendance check-out. The format should be `QR__`, where is the ID of the attendance device and is in the format `YYYY-MM-DD`.\n    \n- **deviceId** (string): The unique identifier for the device being used for the check-out.\n    \n\n#### Example Request Body\n\n``` json\n{\n  \"attendanceCode\": \"QR_MyDevice_2025-06-11\",\n  \"deviceId\": \"5668986\"\n}\n\n ```\n\n### Response\n\nOn a successful request, the response will return a status code of `200` and a JSON object containing the following properties:\n\n- **success** (boolean): Indicates if the operation was successful.\n    \n- **message** (string): A message related to the operation, which may be empty.\n    \n- **body** (object): Contains detailed information about the check-out process, including:\n    \n    - **id** (string): Identifier for the attendance record.\n        \n    - **employee** (object): Information about the employee, including:\n        \n        - **id** (string): Employee ID.\n            \n        - **ecNumber** (string): Employee code number.\n            \n        - **fullName** (string): Full name of the employee.\n            \n        - **devices** (array): List of devices associated with the employee.\n            \n        - **role** (string): Role of the employee.\n            \n        - **username** (string): Username of the employee.\n            \n        - **enabled** (boolean): Indicates if the employee account is enabled.\n            \n        - **accountNonExpired** (boolean): Indicates if the employee account is non-expired.\n            \n        - **accountNonLocked** (boolean): Indicates if the employee account is non-locked.\n            \n        - **credentialsNonExpired** (boolean): Indicates if the employee credentials are non-expired.\n            \n        - **authorities** (array): List of authorities assigned to the employee.\n            \n    - **attendanceDevice** (object): Information about the attendance device used, including:\n        \n        - **id** (string): Identifier for the attendance device.\n            \n        - **deviceId** (string): Device ID.\n            \n        - **location** (string): Location of the device.\n            \n        - **floor** (string): Floor where the device is located.\n            \n    - **checkInDate** (string): Date of check-in.\n        \n    - **checkInTime** (string): Time of check-in.\n        \n    - **checkOutTime** (string): Time of check-out.\n        \n    - **actualLocation** (string or null): Actual location during check-out.\n        \n    - **attendanceCode** (string): The attendance code used for check-out.\n        \n\n### Example Response\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": {\n    \"id\": \"\",\n    \"employee\": {\n      \"id\": \"\",\n      \"ecNumber\": \"\",\n      \"fullName\": \"\",\n      \"devices\": [\"\"],\n      \"role\": \"\",\n      \"username\": \"\",\n      \"enabled\": true,\n      \"accountNonExpired\": true,\n      \"accountNonLocked\": true,\n      \"credentialsNonExpired\": true,\n      \"authorities\": [{\"authority\": \"\"}]\n    },\n    \"attendanceDevice\": {\n      \"id\": \"\",\n      \"deviceId\": \"\",\n      \"location\": \"\",\n      \"floor\": \"\"\n    },\n    \"checkInDate\": \"\",\n    \"checkInTime\": \"\",\n    \"checkOutTime\": \"\",\n    \"actualLocation\": null,\n    \"attendanceCode\": \"\"\n  }\n}\n\n ```"}, "response": [{"name": "Already Checked Out Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"attendanceCode\": \"QR_My Device_2025-06-11\",\n    \"deviceId\": \"5668986\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-log/check-out", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "check-out"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 11 Jun 2025 13:01:35 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Check-out already recorded for today.\",\n    \"body\": null\n}"}, {"name": "Successful Check Out", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"attendanceCode\": \"QR_My Device_2025-06-11\",\n    \"deviceId\": \"5668986\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://************:8170/api/attendance-log/check-out", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "check-out"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 11 Jun 2025 13:40:32 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Check-out successful\",\n    \"body\": {\n        \"id\": \"6849856fbad96dad988d377b\",\n        \"employee\": {\n            \"id\": \"68497ce2bad96dad988d3778\",\n            \"ecNumber\": \"157638\",\n            \"fullName\": \"Blessed Tawan<PERSON>\",\n            \"devices\": [\n                \"5668986\"\n            ],\n            \"role\": \"ROLE_ADMIN\",\n            \"password\": null,\n            \"authorities\": [\n                {\n                    \"authority\": \"ROLE_ADMIN\"\n                }\n            ],\n            \"username\": \"157638\",\n            \"enabled\": true,\n            \"accountNonExpired\": true,\n            \"accountNonLocked\": true,\n            \"credentialsNonExpired\": true\n        },\n        \"attendanceDevice\": {\n            \"id\": \"68497d02bad96dad988d3779\",\n            \"deviceId\": \"My Device\",\n            \"location\": \"Kopje Plaza HQ\",\n            \"floor\": \"10\"\n        },\n        \"checkInDate\": \"2025-06-11\",\n        \"checkInTime\": \"15:32:31.*********\",\n        \"checkOutTime\": \"15:40:32.*********\",\n        \"actualLocation\": null,\n        \"attendanceCode\": \"QR_My Device_2025-06-11\"\n    }\n}"}]}, {"name": "Get Attendance Logs By EcNumber or By AttendanceDeviceID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "GET", "header": [{"key": "", "value": "", "type": "text"}], "url": {"raw": "http://************:8170/api/attendance-log?ecNumber=157638&attendanceDeviceId=My Device", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log"], "query": [{"key": "ecNumber", "value": "157638"}, {"key": "attendanceDeviceId", "value": "My Device"}]}, "description": "### Attendance Log Retrieval\n\nThis endpoint allows you to retrieve attendance logs based on specific parameters. You can query the attendance logs using either the `ecNumber`, the `attendanceDeviceId`, or both.\n\n#### Request\n\n- **Method**: GET\n    \n- **URL**: `http://************:8170/api/attendance-log`\n    \n- **Query Parameters**:\n    \n    - `ecNumber` (string): The employee code number to filter the attendance logs.\n        \n    - `attendanceDeviceId` (string): The ID of the attendance device to filter the logs.\n        \n\n#### Response\n\n- **Status**: 200 OK\n    \n- **Content-Type**: application/json\n    \n- **Body**:\n    \n    - `success` (boolean): Indicates if the request was successful.\n        \n    - `message` (string): A message providing additional information about the response.\n        \n    - `body` (array): An array of attendance log entries, where each entry contains:\n        \n        - `id` (string): The unique identifier for the attendance log.\n            \n        - `employee` (object): Details about the employee, including:\n            \n            - `id` (string): The unique identifier for the employee.\n                \n            - `ecNumber` (string): The employee code number.\n                \n            - `fullName` (string): The full name of the employee.\n                \n            - `devices` (array): An array of devices associated with the employee.\n                \n            - `role` (string): The role of the employee.\n                \n            - `password` (string|null): The password of the employee (if applicable).\n                \n            - `authorities` (array): An array of authority objects associated with the employee.\n                \n            - `username` (string): The username of the employee.\n                \n            - `enabled` (boolean): Indicates if the employee account is enabled.\n                \n            - `accountNonExpired` (boolean): Indicates if the employee account is non-expired.\n                \n            - `accountNonLocked` (boolean): Indicates if the employee account is non-locked.\n                \n            - `credentialsNonExpired` (boolean): Indicates if the employee credentials are non-expired.\n                \n        - `attendanceDevice` (object): Details about the attendance device used, including:\n            \n            - `id` (string): The unique identifier for the device.\n                \n            - `deviceId` (string): The ID of the attendance device.\n                \n            - `location` (string): The location of the attendance device.\n                \n            - `floor` (string): The floor where the device is located.\n                \n        - `checkInDate` (string): The date of check-in.\n            \n        - `checkInTime` (string): The time of check-in.\n            \n        - `checkOutTime` (string): The time of check-out.\n            \n        - `actualLocation` (string|null): The actual location of the employee during attendance.\n            \n        - `attendanceCode` (string): The attendance code associated with the log.\n            \n\nThis endpoint provides a comprehensive view of attendance logs based on the specified parameters.\n\n### Example\n\nTo retrieve attendance logs for an employee with `ecNumber` 157638, the request would look like this:\n\n```\nGET http://************:8170/api/attendance-log?ecNumber=157638\n\n ```\n\nAlternatively, to get logs for a specific device, use:\n\n```\nGET http://************:8170/api/attendance-log?attendanceDeviceId=My Device\n\n ```\n\n### Example Response\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": [\n    {\n      \"id\": \"\",\n      \"employee\": {\n        \"id\": \"\",\n        \"ecNumber\": \"\",\n        \"fullName\": \"\",\n        \"devices\": [\"\"],\n        \"role\": \"\",\n        \"password\": null,\n        \"authorities\": [\n          {\n            \"authority\": \"\"\n          }\n        ],\n        \"username\": \"\",\n        \"enabled\": true,\n        \"accountNonExpired\": true,\n        \"accountNonLocked\": true,\n        \"credentialsNonExpired\": true\n      },\n      \"attendanceDevice\": {\n        \"id\": \"\",\n        \"deviceId\": \"\",\n        \"location\": \"\",\n        \"floor\": \"\"\n      },\n      \"checkInDate\": \"\",\n      \"checkInTime\": \"\",\n      \"checkOutTime\": \"\",\n      \"actualLocation\": null,\n      \"attendanceCode\": \"\"\n    }\n  ]\n}\n\n ```"}, "response": []}, {"name": "Get Attendance Logs By Date", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJpZCI6IjY4NDk0ODcwNzk1NjI5YzEzMDgyOTgzZSIsInN1YiI6IjE1NzYzOCJ9.rYFTGR0MxaDjSJrTIDHEcnW5WmpjeEXX188aP6fs5mMZmrB39ZiezHIXjPVxvCLpulyUYOoAR8V9PFUNc4kxZw", "type": "string"}]}, "method": "GET", "header": [{"key": "", "value": "", "type": "text"}], "url": {"raw": "http://************:8170/api/attendance-log/date/2025-06-11", "protocol": "http", "host": ["10", "95", "10", "128"], "port": "8170", "path": ["api", "attendance-log", "date", "2025-06-11"], "query": [{"key": "ecNumber", "value": "157638", "disabled": true}]}, "description": "## API Endpoint: Retrieve Attendance Log by Date\n\n### Description\n\nThis endpoint allows you to retrieve the attendance logs for a specific date. The request is made using the HTTP GET method, and it returns information about employee attendance, including check-in and check-out times, as well as the devices used for attendance.\n\n### Request\n\n- **Method**: GET\n    \n- **URL**: `http://************:8170/api/attendance-log/date/{date}`\n    \n- **Path Parameter**:\n    \n    - `date` (string): The date for which the attendance log is requested, formatted as `YYYY-MM-DD`.\n        \n\n### Response\n\nThe response is returned in JSON format and contains the following structure:\n\n- **success** (boolean): Indicates whether the request was successful.\n    \n- **message** (string): A message providing additional information about the request (if any).\n    \n- **body** (array): An array containing attendance records for the specified date. Each record includes:\n    \n    - **id** (string): The unique identifier for the attendance record.\n        \n    - **employee** (object): Contains details about the employee:\n        \n        - **id** (string): The unique identifier for the employee.\n            \n        - **ecNumber** (string): The employee's identification number.\n            \n        - **fullName** (string): The full name of the employee.\n            \n        - **devices** (array): A list of devices associated with the employee.\n            \n        - **role** (string): The role of the employee.\n            \n        - **password** (null): The password field (not returned for security reasons).\n            \n        - **authorities** (array): A list of authorities associated with the employee, each containing:\n            \n            - **authority** (string): The authority granted to the employee.\n                \n        - **username** (string): The username of the employee.\n            \n        - **enabled** (boolean): Indicates if the employee account is enabled.\n            \n        - **accountNonExpired** (boolean): Indicates if the employee account is not expired.\n            \n        - **accountNonLocked** (boolean): Indicates if the employee account is not locked.\n            \n        - **credentialsNonExpired** (boolean): Indicates if the employee credentials are not expired.\n            \n    - **attendanceDevice** (object): Contains details about the device used for attendance:\n        \n        - **id** (string): The unique identifier for the device.\n            \n        - **deviceId** (string): The device ID.\n            \n        - **location** (string): The location of the device.\n            \n        - **floor** (string): The floor where the device is located.\n            \n    - **checkInDate** (string): The date the employee checked in.\n        \n    - **checkInTime** (string): The time the employee checked in.\n        \n    - **checkOutTime** (string): The time the employee checked out.\n        \n    - **actualLocation** (null): The actual location field (may not be provided).\n        \n    - **attendanceCode** (string): The code associated with the attendance record.\n        \n\n### Example Response\n\n``` json\n{\n  \"success\": true,\n  \"message\": \"\",\n  \"body\": [\n    {\n      \"id\": \"\",\n      \"employee\": {\n        \"id\": \"\",\n        \"ecNumber\": \"\",\n        \"fullName\": \"\",\n        \"devices\": [\"\"],\n        \"role\": \"\",\n        \"password\": null,\n        \"authorities\": [\n          {\n            \"authority\": \"\"\n          }\n        ],\n        \"username\": \"\",\n        \"enabled\": true,\n        \"accountNonExpired\": true,\n        \"accountNonLocked\": true,\n        \"credentialsNonExpired\": true\n      },\n      \"attendanceDevice\": {\n        \"id\": \"\",\n        \"deviceId\": \"\",\n        \"location\": \"\",\n        \"floor\": \"\"\n      },\n      \"checkInDate\": \"\",\n      \"checkInTime\": \"\",\n      \"checkOutTime\": \"\",\n      \"actualLocation\": null,\n      \"attendanceCode\": \"\"\n    }\n  ]\n}\n\n ```"}, "response": []}]}