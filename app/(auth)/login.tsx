import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { useAuth } from '@/context/AuthContext';
import { router } from 'expo-router';
import Colors from '@/constants/Colors';
import { useColorScheme } from 'react-native';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react-native';
import CheckInButton from '@/components/CheckInButton';
import Logo from '@/assets/images/icon.png';
import { BlurView } from 'expo-blur';

export default function LoginScreen() {
  const { signIn, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const handleLogin = async () => {
    // Reset errors
    setEmailError('');
    setPasswordError('');

    // Validate inputs
    let isValid = true;

    if (!email) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    }

    if (!isValid) return;

    // Attempt sign in
    const success = await signIn(email, password);

    if (success) {
      router.replace('/(tabs)');
    } else {
      Alert.alert(
        'Login Failed',
        'Invalid email or password. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.backgroundContainer}>
        {/* Add an overall BlurView with lower intensity as a base layer */}
        <BlurView
          intensity={20}
          tint="default"
          style={StyleSheet.absoluteFill}
        />

        {/* Create colored circles without BlurView wrapping */}
        <View style={[styles.orangeCircle, styles.topLeftCircle]} />
        <View style={[styles.orangeCircle, styles.bottomRightCircle]} />
        <View style={[styles.orangeCircle, styles.centerTopCircle]} />
        <View style={[styles.orangeCircle, styles.centerBottomCircle]} />
        <View style={[styles.orangeCircle, styles.rightCenterCircle]} />

        {/* Add a stronger BlurView on top for an enhanced effect */}
        <BlurView
          intensity={60}
          tint={colorScheme === 'dark' ? 'dark' : 'light'}
          style={[StyleSheet.absoluteFill, { zIndex: 1 }]}
        />
      </View>

      <ScrollView
        contentContainerStyle={[
          styles.container,
          { backgroundColor: 'transparent' },
        ]}
      >
        <View style={styles.logoContainer}>
          <Image source={Logo} style={styles.logo} resizeMode="contain" />
          <Text
            style={[
              styles.appName,
              { color: colors.text, fontFamily: 'Inter-Bold' },
            ]}
          >
            NetOne Attendance
          </Text>
          <Text
            style={[
              styles.tagline,
              { color: colors.placeholder, fontFamily: 'Inter-Regular' },
            ]}
          >
            Sign in with your work email
          </Text>
        </View>

        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text
              style={[
                styles.label,
                { color: colors.text, fontFamily: 'Inter-Medium' },
              ]}
            >
              Email Address
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  borderColor: emailError ? colors.error : colors.border,
                  backgroundColor: colors.card,
                },
              ]}
            >
              <Mail
                size={20}
                color={colors.placeholder}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  {
                    color: colors.text,
                    fontFamily: 'Inter-Regular',
                    height: 35,
                  },
                ]}
                placeholder="<EMAIL>"
                placeholderTextColor={colors.placeholder}
                keyboardType="email-address"
                autoCapitalize="none"
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  setEmailError('');
                }}
              />
            </View>
            {emailError ? (
              <Text
                style={[
                  styles.errorText,
                  { color: colors.error, fontFamily: 'Inter-Regular' },
                ]}
              >
                {emailError}
              </Text>
            ) : null}
          </View>

          <View style={styles.inputGroup}>
            <Text
              style={[
                styles.label,
                { color: colors.text, fontFamily: 'Inter-Medium' },
              ]}
            >
              Password
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  borderColor: passwordError ? colors.error : colors.border,
                  backgroundColor: colors.card,
                },
              ]}
            >
              <Lock
                size={20}
                color={colors.placeholder}
                style={styles.inputIcon}
              />
              <TextInput
                style={[
                  styles.input,
                  {
                    color: colors.text,
                    fontFamily: 'Inter-Regular',
                    height: 35,
                  },
                ]}
                placeholder="Your password"
                placeholderTextColor={colors.placeholder}
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={(text) => {
                  setPassword(text);
                  setPasswordError('');
                }}
              />
              <TouchableOpacity
                style={styles.visibilityToggle}
                onPress={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff size={20} color={colors.placeholder} />
                ) : (
                  <Eye size={20} color={colors.placeholder} />
                )}
              </TouchableOpacity>
            </View>
            {passwordError ? (
              <Text
                style={[
                  styles.errorText,
                  { color: colors.error, fontFamily: 'Inter-Regular' },
                ]}
              >
                {passwordError}
              </Text>
            ) : null}
          </View>

          <CheckInButton
            title="Sign In"
            onPress={handleLogin}
            isLoading={isLoading}
            style={styles.loginButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  backgroundContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
  orangeCircle: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 140, 0, 0.5)', // Increased opacity for better visibility
  },
  topLeftCircle: {
    top: -80,
    left: -60,
    width: 200,
    height: 200,
    borderRadius: 100,
  },
  bottomRightCircle: {
    bottom: -70,
    right: -40,
    width: 220,
    height: 220,
    borderRadius: 110,
    backgroundColor: 'rgba(255, 160, 0, 0.60)', // Different orange shade
  },
  centerTopCircle: {
    top: '15%',
    right: '20%',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(213, 163, 138, 0.5)',
  },
  centerBottomCircle: {
    bottom: '25%',
    left: '10%',
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  rightCenterCircle: {
    top: '45%',
    right: -30,
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 150,
    height: 150,
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
  },
  formContainer: {
    width: '100%',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 0,
  },
  visibilityToggle: {
    padding: 4,
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    fontSize: 14,
  },
  loginButton: {
    marginBottom: 24,
    height: 60,
  },
  helpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
    marginTop: 16,
  },
  helpText: {
    fontSize: 14,
  },
  contactText: {
    fontSize: 14,
  },
});
