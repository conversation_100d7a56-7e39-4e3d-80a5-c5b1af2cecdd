import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  useColorScheme,
  ActivityIndicator,
  Modal,
  Image,
} from 'react-native';
import Colors from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import QRScanner from '@/components/QRScanner';
import {
  QrCode,
  Scan,
  X,
  Check,
  Clock,
  LogOut,
  Calendar,
} from 'lucide-react-native';
import {
  checkIn,
  checkOut,
  getCurrentCheckInStatus,
} from '@/services/attendanceService';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { useRouter } from 'expo-router';

export default function ScanScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const [isScannerVisible, setIsScannerVisible] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [scanResult, setScanResult] = useState<string | null>(null);
  const [mode, setMode] = useState<'checkin' | 'checkout'>('checkin');
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);
  const [currentRecordId, setCurrentRecordId] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<'work' | 'event'>('work');
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [currentQrData, setCurrentQrData] = useState<string | null>(null);

  // Check if user is already checked in when component loads
  useEffect(() => {
    const checkStatus = async () => {
      if (user) {
        setIsCheckingStatus(true);
        try {
          const status = await getCurrentCheckInStatus(user.id);
          if (status) {
            setIsCheckedIn(true);
            setCurrentRecordId(status.id);
            setMode('checkout'); // Default to checkout if already checked in
          } else {
            setIsCheckedIn(false);
            setMode('checkin');
          }
        } catch (error) {
          console.error('Failed to check status:', error);
        } finally {
          setIsCheckingStatus(false);
        }
      }
    };

    checkStatus();
  }, [user]);

  // Reset success state after a few seconds
  useEffect(() => {
    if (isSuccess) {
      const timer = setTimeout(() => {
        setIsSuccess(false);
        setScanResult(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isSuccess]);

  // Handle QR code scan
  const handleScan = async (data: string) => {
    try {
      setIsProcessing(true);
      setScanResult(data);
      setIsScannerVisible(false);

      // Simulating API call delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (user) {
        if (mode === 'checkin') {
          // Handle check-in
          const record = await checkIn(
            user.id,
            selectedType,
            selectedType === 'event' ? 'Office Meeting' : undefined,
            'NetOne Office'
          );

          setCurrentRecordId(record.id);
          setIsCheckedIn(true);

          // Show success message
          Alert.alert(
            'Check-In Successful',
            `Your ${
              selectedType === 'work' ? 'work' : 'event'
            } attendance has been recorded successfully.`,
            [{ text: 'OK' }]
          );
        } else {
          // Handle check-out
          if (!currentRecordId) {
            throw new Error('No active check-in found');
          }

          await checkOut(currentRecordId);
          setIsCheckedIn(false);
          setCurrentRecordId(null);

          // Show success message
          Alert.alert(
            'Check-Out Successful',
            `Your ${
              selectedType === 'work' ? 'work' : 'event'
            } check-out has been recorded successfully.`,
            [{ text: 'OK' }]
          );
        }

        // Provide haptic feedback on non-web platforms
        if (Platform.OS !== 'web') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }

        setIsSuccess(true);
      }
    } catch (error) {
      console.error('QR scan error:', error);
      Alert.alert(
        mode === 'checkin' ? 'Check-In Failed' : 'Check-Out Failed',
        'There was an error while processing your QR code. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Toggle between check-in and check-out modes
  const toggleMode = () => {
    setMode((prev) => (prev === 'checkin' ? 'checkout' : 'checkin'));
  };

  // Select attendance type (work or event)
  const selectType = (type: 'work' | 'event') => {
    setSelectedType(type);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {isScannerVisible ? (
        <>
          <QRScanner
            onScan={(data) => {
              // Stop scanning immediately when data is detected
              setIsScannerVisible(false);
              setCurrentQrData(data);
              // Show confirmation modal instead of processing directly
              setIsConfirmModalVisible(true);
            }}
            isVisible={isScannerVisible}
          />
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: colors.card }]}
            onPress={() => setIsScannerVisible(false)}
          >
            <X size={24} color={colors.text} />
          </TouchableOpacity>

          <View
            style={[styles.scanModeIndicator, { backgroundColor: colors.card }]}
          >
            <Text style={[styles.scanModeText, { color: colors.text }]}>
              {mode === 'checkin'
                ? 'Scanning for Check-In'
                : 'Scanning for Check-Out'}
            </Text>
          </View>
        </>
      ) : (
        <View style={styles.content}>
          {isCheckingStatus ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.text }]}>
                Checking your status...
              </Text>
            </View>
          ) : (
            <>
              <View style={styles.iconContainer}>
                {isSuccess ? (
                  <View
                    style={[
                      styles.successIconCircle,
                      { backgroundColor: colors.success },
                    ]}
                  >
                    <Check size={48} color="white" />
                  </View>
                ) : (
                  <View
                    style={[
                      styles.qrIconCircle,
                      {
                        backgroundColor:
                          mode === 'checkin'
                            ? colors.primary
                            : colors.secondary,
                      },
                    ]}
                  >
                    {mode === 'checkin' ? (
                      <QrCode size={48} color="white" />
                    ) : (
                      <LogOut size={48} color="white" />
                    )}
                  </View>
                )}
              </View>

              <Text
                style={[
                  styles.title,
                  { color: colors.text, fontFamily: 'Inter-Bold' },
                ]}
              >
                {isSuccess
                  ? mode === 'checkin'
                    ? 'Check-In Successful!'
                    : 'Check-Out Successful!'
                  : mode === 'checkin'
                  ? 'Scan to Check In'
                  : 'Scan to Check Out'}
              </Text>

              <Text
                style={[
                  styles.description,
                  { color: colors.placeholder, fontFamily: 'Inter-Regular' },
                ]}
              >
                {isSuccess
                  ? mode === 'checkin'
                    ? 'Your attendance has been recorded successfully.'
                    : 'Your check-out has been recorded successfully.'
                  : scanResult
                  ? 'Processing QR code...'
                  : mode === 'checkin'
                  ? 'Scan the QR code at your workplace to record your attendance.'
                  : 'Scan the QR code to record your check-out.'}
              </Text>

              {!isSuccess && !scanResult && (
                <>
                  <View style={styles.typeSelector}>
                    <Text
                      style={[
                        styles.typeSelectorTitle,
                        { color: colors.text, fontFamily: 'Inter-Medium' },
                      ]}
                    >
                      {mode === 'checkin' ? 'Check-In Type' : 'Check-Out Type'}:
                    </Text>
                    <View style={styles.typeButtonsContainer}>
                      <TouchableOpacity
                        style={[
                          styles.typeButton,
                          selectedType === 'work' && {
                            backgroundColor:
                              mode === 'checkin'
                                ? colors.primary + '20'
                                : colors.secondary + '20',
                            borderColor:
                              mode === 'checkin'
                                ? colors.primary
                                : colors.secondary,
                          },
                          selectedType !== 'work' && {
                            borderColor: colors.border,
                          },
                        ]}
                        onPress={() => selectType('work')}
                      >
                        <Clock
                          size={20}
                          color={colors.primary}
                          style={styles.typeIcon}
                        />
                        <Text
                          style={[
                            styles.typeButtonText,
                            {
                              color:
                                selectedType === 'work'
                                  ? mode === 'checkin'
                                    ? colors.primary
                                    : colors.secondary
                                  : colors.text,
                            },
                          ]}
                        >
                          Work
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.typeButton,
                          selectedType === 'event' && {
                            backgroundColor:
                              mode === 'checkin'
                                ? colors.primary + '20'
                                : colors.secondary + '20',
                            borderColor:
                              mode === 'checkin'
                                ? colors.primary
                                : colors.secondary,
                          },
                          selectedType !== 'event' && {
                            borderColor: colors.border,
                          },
                        ]}
                        onPress={() => selectType('event')}
                      >
                        <Calendar
                          size={20}
                          color={colors.primary}
                          style={styles.typeIcon}
                        />
                        <Text
                          style={[
                            styles.typeButtonText,
                            {
                              color:
                                selectedType === 'event'
                                  ? mode === 'checkin'
                                    ? colors.primary
                                    : colors.secondary
                                  : colors.text,
                            },
                          ]}
                        >
                          Event
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <TouchableOpacity
                    style={[
                      styles.scanButton,
                      {
                        backgroundColor:
                          mode === 'checkin'
                            ? colors.primary
                            : colors.secondary,
                      },
                    ]}
                    onPress={() => setIsScannerVisible(true)}
                    activeOpacity={0.8}
                  >
                    <Scan size={24} color="white" style={styles.scanIcon} />
                    <Text
                      style={[
                        styles.scanButtonText,
                        { color: 'white', fontFamily: 'Inter-SemiBold' },
                      ]}
                    >
                      {mode === 'checkin'
                        ? 'Scan to Check In'
                        : 'Scan to Check Out'}
                    </Text>
                  </TouchableOpacity>

                  {isCheckedIn ? (
                    <TouchableOpacity
                      style={[
                        styles.toggleModeButton,
                        { borderColor: colors.border },
                      ]}
                      onPress={toggleMode}
                    >
                      <Clock
                        size={20}
                        color={colors.primary}
                        style={{ marginRight: 8 }}
                      />
                      <Text
                        style={[styles.toggleModeText, { color: colors.text }]}
                      >
                        Switch to{' '}
                        {mode === 'checkin' ? 'Check-Out' : 'Check-In'} Mode
                      </Text>
                    </TouchableOpacity>
                  ) : null}
                </>
              )}

              {scanResult && !isSuccess && (
                <View
                  style={[
                    styles.loadingContainer,
                    { borderColor: colors.border },
                  ]}
                >
                  <View style={styles.loadingDots}>
                    <View
                      style={[
                        styles.loadingDot,
                        { backgroundColor: colors.primary },
                      ]}
                    />
                    <View
                      style={[
                        styles.loadingDot,
                        styles.loadingDotDelay1,
                        { backgroundColor: colors.primary },
                      ]}
                    />
                    <View
                      style={[
                        styles.loadingDot,
                        styles.loadingDotDelay2,
                        { backgroundColor: colors.primary },
                      ]}
                    />
                  </View>
                  <Text
                    style={[
                      styles.loadingText,
                      { color: colors.text, fontFamily: 'Inter-Medium' },
                    ]}
                  >
                    Processing...
                  </Text>
                </View>
              )}

              <View style={styles.instructionsContainer}>
                <Text
                  style={[
                    styles.instructionsTitle,
                    { color: colors.text, fontFamily: 'Inter-SemiBold' },
                  ]}
                >
                  How to use:
                </Text>
                <View style={styles.instructionsList}>
                  <View style={styles.instructionItem}>
                    <View
                      style={[
                        styles.instructionNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <Text
                        style={[
                          styles.instructionNumberText,
                          { fontFamily: 'Inter-Bold' },
                        ]}
                      >
                        1
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.instructionText,
                        { color: colors.text, fontFamily: 'Inter-Regular' },
                      ]}
                    >
                      Tap the "Scan" button above
                    </Text>
                  </View>
                  <View style={styles.instructionItem}>
                    <View
                      style={[
                        styles.instructionNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <Text
                        style={[
                          styles.instructionNumberText,
                          { fontFamily: 'Inter-Bold' },
                        ]}
                      >
                        2
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.instructionText,
                        { color: colors.text, fontFamily: 'Inter-Regular' },
                      ]}
                    >
                      Point your camera at the QR code
                    </Text>
                  </View>
                  <View style={styles.instructionItem}>
                    <View
                      style={[
                        styles.instructionNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <Text
                        style={[
                          styles.instructionNumberText,
                          { fontFamily: 'Inter-Bold' },
                        ]}
                      >
                        3
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.instructionText,
                        { color: colors.text, fontFamily: 'Inter-Regular' },
                      ]}
                    >
                      Wait for confirmation of successful{' '}
                      {mode === 'checkin' ? 'check-in' : 'check-out'}
                    </Text>
                  </View>
                </View>
              </View>
            </>
          )}
        </View>
      )}

      {/* Confirmation Modal */}
      <Modal
        visible={isConfirmModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => {
          setIsConfirmModalVisible(false);
        }}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[styles.confirmationModal, { backgroundColor: colors.card }]}
          >
            {/* User Info Header */}
            <View style={styles.confirmHeader}>
              <View
                style={[
                  styles.avatarCircle,
                  { backgroundColor: colors.primary + '20' },
                ]}
              >
                <Text style={[styles.avatarText, { color: colors.primary }]}>
                  {user?.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </Text>
              </View>
              <View style={styles.confirmHeaderTextContainer}>
                <Text
                  style={[styles.confirmHeaderName, { color: colors.text }]}
                >
                  {user?.name || 'User'}
                </Text>
                <Text
                  style={[
                    styles.confirmHeaderEmail,
                    { color: colors.placeholder },
                  ]}
                >
                  {user?.email || '<EMAIL>'}
                </Text>
              </View>
            </View>

            {/* Status Section */}
            <View style={styles.statusSection}>
              <Text style={[styles.statusLabel, { color: colors.placeholder }]}>
                Status
              </Text>
              <View style={styles.statusRow}>
                <Text
                  style={[styles.confirmStatusText, { color: colors.success }]}
                >
                  {mode === 'checkin' ? 'Going' : 'Leaving'}
                </Text>
                <Text
                  style={[styles.confirmStatusTime, { color: colors.text }]}
                >
                  Today at{' '}
                  {new Date().toLocaleTimeString([], {
                    hour: 'numeric',
                    minute: '2-digit',
                  })}
                </Text>
              </View>
            </View>

            {/* QR Preview */}
            <View style={styles.qrPreview}>
              <View style={styles.qrCodeContainer}>
                <Image
                  source={require('@/assets/images/qr-placeholder.png')}
                  style={styles.qrImage}
                  defaultSource={require('@/assets/images/qr-placeholder.png')}
                />
              </View>
            </View>

            {/* Confirmation Button */}
            <TouchableOpacity
              style={[
                styles.confirmButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={() => {
                // Close confirmation modal
                setIsConfirmModalVisible(false);
                // Process the scan with the stored QR data
                if (currentQrData) {
                  handleScan(currentQrData);
                }
              }}
            >
              <Text style={[styles.confirmButtonText, { color: 'white' }]}>
                Check {mode === 'checkin' ? 'In' : 'Out'}
              </Text>
            </TouchableOpacity>

            {/* Cancel Button */}
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setIsConfirmModalVisible(false);
                setCurrentQrData(null);
              }}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    top: 48,
    right: 24,
    height: 48,
    width: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  scanModeIndicator: {
    position: 'absolute',
    bottom: 48,
    alignSelf: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    zIndex: 10,
  },
  scanModeText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  iconContainer: {
    marginBottom: 24,
  },
  qrIconCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 24,
  },
  scanButton: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  scanIcon: {
    marginRight: 8,
  },
  scanButtonText: {
    fontSize: 16,
  },
  toggleModeButton: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  toggleModeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  loadingDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  loadingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    opacity: 0.6,
    transform: [{ scale: 1 }],
    animationKeyframes: 'pulse',
    animationDuration: '1.5s',
    animationIterationCount: 'infinite',
  },
  loadingDotDelay1: {
    animationDelay: '0.3s',
  },
  loadingDotDelay2: {
    animationDelay: '0.6s',
  },
  instructionsContainer: {
    width: '100%',
    marginTop: 16,
  },
  instructionsTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  instructionsList: {
    gap: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  instructionNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionNumberText: {
    color: 'white',
    fontSize: 16,
  },
  instructionText: {
    fontSize: 16,
    flex: 1,
  },
  typeSelector: {
    marginBottom: 16,
  },
  typeSelectorTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  typeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
    marginHorizontal: 4,
  },
  typeIcon: {
    marginRight: 8,
  },
  typeButtonText: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmationModal: {
    width: '80%',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  confirmHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
  },
  confirmHeaderTextContainer: {
    flex: 1,
  },
  confirmHeaderName: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
  },
  confirmHeaderEmail: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  confirmHeaderStatus: {
    alignItems: 'flex-end',
  },
  confirmStatusText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  confirmStatusTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  qrPreview: {
    width: '100%',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  qrCodeContainer: {
    width: 150,
    height: 150,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  confirmButton: {
    width: '100%',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  confirmButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  cancelButton: {
    width: '100%',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
});
