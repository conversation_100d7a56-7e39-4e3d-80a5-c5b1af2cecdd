import { useEffect } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@/context/AuthContext';
import { ActivityIndicator } from 'react-native';
import Colors from '@/constants/Colors';
import { useColorScheme } from 'react-native';
import Logo from '@/assets/images/icon.png';

export default function StartupScreen() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  useEffect(() => {
    // Navigate based on authentication state
    const navigate = async () => {
      if (isLoading) return;

      if (user) {
        router.replace('/(tabs)');
      } else {
        router.replace('/(auth)/login');
      }
    };

    navigate();
  }, [user, isLoading, router]);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Image source={Logo} style={styles.logo} resizeMode="contain" />
      <Text
        style={[styles.title, { color: colors.text, fontFamily: 'Inter-Bold' }]}
      >
        NetOne Attendance
      </Text>
      <ActivityIndicator
        size="large"
        color={colors.primary}
        style={styles.loading}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  logo: {
    width: 150,
    height: 150,
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loading: {
    marginTop: 32,
  },
});
