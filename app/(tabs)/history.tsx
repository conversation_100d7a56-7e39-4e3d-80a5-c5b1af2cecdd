import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import Colors from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { Calendar, Filter } from 'lucide-react-native';
import { getUserAttendanceRecords, AttendanceRecord } from '@/services/attendanceService';
import AttendanceCard from '@/components/AttendanceCard';

export default function HistoryScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  
  const [records, setRecords] = useState<AttendanceRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'work' | 'event'>('all');
  
  // Load attendance records
  useEffect(() => {
    const loadRecords = async () => {
      if (user) {
        try {
          setIsLoading(true);
          const data = await getUserAttendanceRecords(user.id);
          setRecords(data);
        } catch (error) {
          console.error('Failed to load attendance records', error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    loadRecords();
  }, [user]);
  
  // Apply filter to records
  const filteredRecords = records.filter(record => {
    if (filter === 'all') return true;
    return record.type === filter;
  });
  
  // Group records by date
  const groupedRecords: { [date: string]: AttendanceRecord[] } = {};
  
  filteredRecords.forEach(record => {
    const date = new Date(record.checkInTime).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
    
    if (!groupedRecords[date]) {
      groupedRecords[date] = [];
    }
    
    groupedRecords[date].push(record);
  });
  
  // Sort dates in descending order (most recent first)
  const sortedDates = Object.keys(groupedRecords).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header with filters */}
      <View style={[styles.header, { backgroundColor: colors.card }]}>
        <View style={styles.filterContainer}>
          <View style={styles.filterLabelContainer}>
            <Filter size={16} color={colors.primary} />
            <Text style={[styles.filterLabel, { color: colors.text, fontFamily: 'Inter-Medium' }]}>
              Filter:
            </Text>
          </View>
          
          <View style={styles.filterButtons}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                { 
                  backgroundColor: filter === 'all' ? colors.primary : 'transparent',
                  borderColor: filter === 'all' ? colors.primary : colors.border,
                }
              ]}
              onPress={() => setFilter('all')}
            >
              <Text 
                style={[
                  styles.filterButtonText, 
                  { 
                    color: filter === 'all' ? 'white' : colors.text,
                    fontFamily: 'Inter-Medium'
                  }
                ]}
              >
                All
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                { 
                  backgroundColor: filter === 'work' ? colors.primary : 'transparent',
                  borderColor: filter === 'work' ? colors.primary : colors.border,
                }
              ]}
              onPress={() => setFilter('work')}
            >
              <Text 
                style={[
                  styles.filterButtonText, 
                  { 
                    color: filter === 'work' ? 'white' : colors.text,
                    fontFamily: 'Inter-Medium'
                  }
                ]}
              >
                Work
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                { 
                  backgroundColor: filter === 'event' ? colors.primary : 'transparent',
                  borderColor: filter === 'event' ? colors.primary : colors.border,
                }
              ]}
              onPress={() => setFilter('event')}
            >
              <Text 
                style={[
                  styles.filterButtonText, 
                  { 
                    color: filter === 'event' ? 'white' : colors.text,
                    fontFamily: 'Inter-Medium'
                  }
                ]}
              >
                Events
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      {/* Attendance Records */}
      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}
      >
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text, fontFamily: 'Inter-Regular' }]}>
              Loading attendance records...
            </Text>
          </View>
        ) : filteredRecords.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Calendar size={48} color={colors.placeholder} style={styles.emptyIcon} />
            <Text style={[styles.emptyTitle, { color: colors.text, fontFamily: 'Inter-Medium' }]}>
              No Records Found
            </Text>
            <Text style={[styles.emptyText, { color: colors.placeholder, fontFamily: 'Inter-Regular' }]}>
              {filter === 'all' 
                ? "You don't have any attendance records yet." 
                : `You don't have any ${filter} attendance records.`}
            </Text>
          </View>
        ) : (
          <>
            {sortedDates.map(date => (
              <View key={date} style={styles.dateGroup}>
                <View style={[styles.dateHeader, { backgroundColor: colors.primary + '15' }]}>
                  <Text style={[styles.dateText, { color: colors.primary, fontFamily: 'Inter-SemiBold' }]}>
                    {date}
                  </Text>
                </View>
                {groupedRecords[date].map(record => (
                  <AttendanceCard key={record.id} record={record} />
                ))}
              </View>
            ))}
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  filterContainer: {
    gap: 12,
  },
  filterLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  filterLabel: {
    fontSize: 16,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  dateGroup: {
    marginBottom: 24,
  },
  dateHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  dateText: {
    fontSize: 14,
  },
});