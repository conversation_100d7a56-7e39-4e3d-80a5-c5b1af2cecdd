import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  useColorScheme,
  Alert,
} from 'react-native';
import Colors from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { Clock, Calendar, BadgeCheck, LogOut } from 'lucide-react-native';
import CheckInButton from '@/components/CheckInButton';
import {
  checkIn,
  checkOut,
  getCurrentCheckInStatus,
  AttendanceRecord,
} from '@/services/attendanceService';
import QRScanModal from '@/components/QRScanModal';
import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

export default function DashboardScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [checkInType, setCheckInType] = useState<'work' | 'event' | null>(null);
  const [showScanner, setShowScanner] = useState(false);
  const [selectedType, setSelectedType] = useState<'work' | 'event'>('work');
  const [currentRecord, setCurrentRecord] = useState<AttendanceRecord | null>(
    null
  );
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);

  // Get current date and time
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const currentTime = new Date().toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });

  // Check if user is already checked in when component loads
  useEffect(() => {
    const checkStatus = async () => {
      if (user) {
        setIsCheckingStatus(true);
        try {
          const status = await getCurrentCheckInStatus(user.id);
          if (status) {
            setIsCheckedIn(true);
            setCheckInType(status.type);
            setCurrentRecord(status);
          }
        } catch (error) {
          console.error('Failed to check status:', error);
        } finally {
          setIsCheckingStatus(false);
        }
      }
    };

    checkStatus();
  }, [user]);

  // Handle check-in
  const handleCheckIn = async (type: 'work' | 'event') => {
    setSelectedType(type);
    setShowScanner(true);
  };

  // Handle check-out
  const handleCheckOut = async (type?: 'work' | 'event') => {
    if (!currentRecord) return;

    setIsLoading(true);

    try {
      // Add haptic feedback on non-web platforms
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      await checkOut(currentRecord.id);

      // Update UI state
      setIsCheckedIn(false);
      setCheckInType(null);
      setCurrentRecord(null);

      // Show confirmation message
      Alert.alert(
        'Check-Out Successful',
        `You have successfully checked out from ${
          type === 'event' || currentRecord.type === 'event'
            ? 'the event'
            : 'work'
        }.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Check-out error:', error);
      Alert.alert(
        'Check-Out Failed',
        'There was an error while checking out. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleScan = async (data: string) => {
    try {
      setIsLoading(true);

      // Add haptic feedback on non-web platforms
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      if (user) {
        const record = await checkIn(
          user.id,
          selectedType,
          selectedType === 'event' ? 'Office Meeting' : undefined,
          'NetOne Office'
        );

        // Update UI state
        setIsCheckedIn(true);
        setCheckInType(selectedType);
        setCurrentRecord(record);
        setShowScanner(false);

        // Show confirmation message
        Alert.alert(
          'Check-In Successful',
          `You have successfully checked in for ${
            selectedType === 'work' ? 'work' : 'the event'
          }.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Check-in error:', error);
      Alert.alert(
        'Check-In Failed',
        'There was an error while checking in. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const checkInOptions = [
    {
      id: 'work',
      title: 'Work Check-In',
      description: 'Regular work day attendance',
      icon: <Clock size={24} color={colors.primary} />,
      onPress: () => handleCheckIn('work'),
    },
    {
      id: 'event',
      title: 'Event Check-In',
      description: 'Meetings, trainings, etc.',
      icon: <Calendar size={24} color={colors.primary} />,
      onPress: () => handleCheckIn('event'),
    },
  ];

  const checkOutOptions = [
    {
      id: 'work',
      title: 'Work Check-Out',
      description: 'End your work day',
      icon: <LogOut size={24} color={colors.primary} />,
      onPress: () => handleCheckOut('work'),
    },
    {
      id: 'event',
      title: 'Event Check-Out',
      description: 'Exit from event or meeting',
      icon: <Calendar size={24} color={colors.primary} />,
      onPress: () => handleCheckOut('event'),
    },
  ];

  // Format time from ISO string
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Header with user info */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Text
            style={[
              styles.greeting,
              { color: colors.text, fontFamily: 'Inter-Regular' },
            ]}
          >
            Hello,
          </Text>
          <Text
            style={[
              styles.userName,
              { color: colors.text, fontFamily: 'Inter-Bold' },
            ]}
          >
            {user?.name || 'User'}
          </Text>
          <View
            style={[
              styles.dateTimeContainer,
              { backgroundColor: colors.primary + '15' },
            ]}
          >
            <Text
              style={[
                styles.dateTime,
                { color: colors.primary, fontFamily: 'Inter-Medium' },
              ]}
            >
              {currentDate} | {currentTime}
            </Text>
          </View>
        </View>
      </View>

      {/* Status Card */}
      <View
        style={[
          styles.statusCard,
          { backgroundColor: colors.card, borderColor: colors.border },
        ]}
      >
        <View style={styles.statusCardHeader}>
          <View style={styles.statusCardIcon}>
            <BadgeCheck size={28} color={colors.primary} />
          </View>
          <View>
            <Text
              style={[
                styles.statusCardTitle,
                { color: colors.text, fontFamily: 'Inter-Bold' },
              ]}
            >
              Today's Status
            </Text>
          </View>
        </View>

        <View
          style={[styles.statusCardDivider, { backgroundColor: colors.border }]}
        />

        {isCheckingStatus ? (
          <View style={styles.loadingStatus}>
            <Text style={[styles.loadingText, { color: colors.text }]}>
              Checking status...
            </Text>
          </View>
        ) : (
          <View style={styles.statusCardBody}>
            {isCheckedIn ? (
              <>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: colors.success + '15' },
                  ]}
                >
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: colors.success },
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusText,
                      { color: colors.success, fontFamily: 'Inter-Medium' },
                    ]}
                  >
                    Checked In
                  </Text>
                </View>
                <Text
                  style={[
                    styles.statusDetails,
                    { color: colors.text, fontFamily: 'Inter-Regular' },
                  ]}
                >
                  You checked in for{' '}
                  {checkInType === 'work'
                    ? 'Work'
                    : currentRecord?.eventName || 'an Event'}{' '}
                  at{' '}
                  {currentRecord
                    ? formatTime(currentRecord.checkInTime)
                    : currentTime}
                </Text>
              </>
            ) : (
              <>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: colors.warning + '15' },
                  ]}
                >
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: colors.warning },
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusText,
                      { color: colors.warning, fontFamily: 'Inter-Medium' },
                    ]}
                  >
                    Not Checked In
                  </Text>
                </View>
                <Text
                  style={[
                    styles.statusDetails,
                    { color: colors.text, fontFamily: 'Inter-Regular' },
                  ]}
                >
                  You haven't checked in yet today
                </Text>
              </>
            )}
          </View>
        )}
      </View>

      {/* Check In Options */}
      <View style={styles.sectionContainer}>
        <Text
          style={[
            styles.sectionTitle,
            { color: colors.text, fontFamily: 'Inter-SemiBold' },
          ]}
        >
          {isCheckedIn ? 'Check-Out Options' : 'Check-In Options'}
        </Text>
        <View style={styles.optionsContainer}>
          {isCheckedIn
            ? // Show checkout options
              checkOutOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.optionCard,
                    {
                      backgroundColor: colors.card,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={option.onPress}
                  disabled={isLoading}
                  activeOpacity={0.8}
                >
                  <View style={styles.optionHeader}>
                    <View
                      style={[
                        styles.optionIconContainer,
                        { backgroundColor: colors.primary + '15' },
                      ]}
                    >
                      {option.icon}
                    </View>
                  </View>
                  <Text
                    style={[
                      styles.optionTitle,
                      { color: colors.text, fontFamily: 'Inter-SemiBold' },
                    ]}
                  >
                    {option.title}
                  </Text>
                  <Text
                    style={[
                      styles.optionDescription,
                      {
                        color: colors.placeholder,
                        fontFamily: 'Inter-Regular',
                      },
                    ]}
                  >
                    {option.description}
                  </Text>
                </TouchableOpacity>
              ))
            : // Show check in options
              checkInOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.optionCard,
                    {
                      backgroundColor: colors.card,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={option.onPress}
                  disabled={isLoading}
                  activeOpacity={0.8}
                >
                  <View style={styles.optionHeader}>
                    <View
                      style={[
                        styles.optionIconContainer,
                        { backgroundColor: colors.primary + '15' },
                      ]}
                    >
                      {option.icon}
                    </View>
                  </View>
                  <Text
                    style={[
                      styles.optionTitle,
                      { color: colors.text, fontFamily: 'Inter-SemiBold' },
                    ]}
                  >
                    {option.title}
                  </Text>
                  <Text
                    style={[
                      styles.optionDescription,
                      {
                        color: colors.placeholder,
                        fontFamily: 'Inter-Regular',
                      },
                    ]}
                  >
                    {option.description}
                  </Text>
                </TouchableOpacity>
              ))}
        </View>
      </View>

      <QRScanModal
        isVisible={showScanner}
        onClose={() => setShowScanner(false)}
        onScan={handleScan}
        isLoading={isLoading}
        type={selectedType}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  userInfo: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
  },
  userName: {
    fontSize: 24,
    marginBottom: 8,
  },
  dateTimeContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  dateTime: {
    fontSize: 12,
  },
  statusCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  statusCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusCardIcon: {
    padding: 8,
  },
  statusCardTitle: {
    fontSize: 18,
  },
  statusCardDate: {
    fontSize: 14,
  },
  statusCardDivider: {
    height: 1,
    marginVertical: 16,
  },
  statusCardBody: {
    gap: 8,
  },
  loadingStatus: {
    alignItems: 'center',
    padding: 8,
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 14,
  },
  statusDetails: {
    fontSize: 14,
    marginBottom: 16,
  },
  checkoutButton: {
    alignSelf: 'flex-start',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  optionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  optionCard: {
    flex: 1,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  currentCheckInCard: {
    flex: 1,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  optionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
  },
  primaryButton: {
    marginTop: 8,
  },
  checkInDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  detailValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
});
