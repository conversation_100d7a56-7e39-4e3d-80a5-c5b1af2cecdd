// Mock attendance service

// Types
export interface AttendanceRecord {
  id: string;
  userId: string;
  type: 'work' | 'event';
  eventName?: string;
  checkInTime: string;
  checkOutTime?: string;
  status: 'checked-in' | 'checked-out';
  location?: string;
}

// Mock data
const MOCK_ATTENDANCE_RECORDS: AttendanceRecord[] = [
  {
    id: '1',
    userId: '1',
    type: 'work',
    checkInTime: '2025-03-14T09:00:00Z',
    checkOutTime: '2025-03-14T17:00:00Z',
    status: 'checked-out',
    location: 'NetOne Office',
  },
  {
    id: '2',
    userId: '1',
    type: 'work',
    checkInTime: '2025-03-15T08:45:00Z',
    checkOutTime: '2025-03-15T17:30:00Z',
    status: 'checked-out',
    location: 'NetOne Office',
  },
  {
    id: '3',
    userId: '1',
    type: 'event',
    eventName: 'Annual Meeting',
    checkInTime: '2025-03-16T10:00:00Z',
    status: 'checked-in',
    location: 'Conference Room A',
  },
];

// Get all attendance records for a user
export const getUserAttendanceRecords = async (
  userId: string
): Promise<AttendanceRecord[]> => {
  // Simulate API call with delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return MOCK_ATTENDANCE_RECORDS.filter((record) => record.userId === userId);
};

// Get the current check-in status for a user (for today)
export const getCurrentCheckInStatus = async (
  userId: string
): Promise<AttendanceRecord | null> => {
  // Simulate API call with delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Get today's date in ISO format (YYYY-MM-DD)
  const today = new Date().toISOString().split('T')[0];

  // Find the most recent checked-in record for today that hasn't been checked out
  const record = MOCK_ATTENDANCE_RECORDS.filter(
    (r) =>
      r.userId === userId &&
      r.status === 'checked-in' &&
      r.checkInTime.startsWith(today)
  ).sort(
    (a, b) =>
      new Date(b.checkInTime).getTime() - new Date(a.checkInTime).getTime()
  )[0];

  return record || null;
};

// Check in
export const checkIn = async (
  userId: string,
  type: 'work' | 'event',
  eventName?: string,
  location?: string
): Promise<AttendanceRecord> => {
  // Simulate API call with delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  const newRecord: AttendanceRecord = {
    id: Date.now().toString(),
    userId,
    type,
    eventName,
    checkInTime: new Date().toISOString(),
    status: 'checked-in',
    location,
  };

  // In a real app, we would send this to an API
  // For now, we'll just add it to our mock data
  MOCK_ATTENDANCE_RECORDS.push(newRecord);

  return newRecord;
};

// Check out
export const checkOut = async (recordId: string): Promise<AttendanceRecord> => {
  // Simulate API call with delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Find the record
  const recordIndex = MOCK_ATTENDANCE_RECORDS.findIndex(
    (r) => r.id === recordId
  );

  if (recordIndex === -1) {
    throw new Error('Record not found');
  }

  // Update the record
  const updatedRecord = {
    ...MOCK_ATTENDANCE_RECORDS[recordIndex],
    checkOutTime: new Date().toISOString(),
    status: 'checked-out' as const,
  };

  // Update in our mock data
  MOCK_ATTENDANCE_RECORDS[recordIndex] = updatedRecord;

  return updatedRecord;
};
