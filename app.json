{"expo": {"name": "NetOne Attendance", "slug": "netone-attendance", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "netone", "userInterfaceStyle": "light", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.emacliam.netone-attendance"}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-camera"], "experiments": {"typedRoutes": true}}}