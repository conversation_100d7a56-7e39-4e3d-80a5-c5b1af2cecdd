{"name": "netone-attendance", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo-google-fonts/inter": "^0.3.0", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native/codegen": "^0.79.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "expo": "53.0.7", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.5", "expo-font": "^13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-router": "~5.0.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.507.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-codegen": "^0.70.7", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/react": "~19.0.14", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.1"}